import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/model/comment/comment_filter_model/comment_filter_model.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';

class AppTextFields {
  //region All text field
  static Widget allTextField({
    required BuildContext context,
    required TextEditingController textEditingController,
    required String hintText,
    int minLines = 1,
    int maxLines = 1,
    maxEntry = 20,
    bool enabled = true,
    dynamic onEditingComplete,
    dynamic onChanged,
    dynamic onSaved,
    TextInputType keyboardType = TextInputType.text,
    TextInputAction textInputAction = TextInputAction.done,
    TextAlign textAlign = TextAlign.start,
    double border = 8.0,
    double? scrollBottomPadding,
    FocusNode? focusNode,
  }) {
    return TextFormField(
      enabled: enabled,
      maxLines: maxLines,
      focusNode: focusNode,
      minLines: minLines,
      textCapitalization: TextCapitalization.sentences,
      inputFormatters: [
        LengthLimitingTextInputFormatter(maxEntry),
        // Filter out pipe character '|' as it's used for separating values in the database
        FilteringTextInputFormatter.deny('|'),
      ],
      textAlign: textAlign,
      textInputAction: textInputAction,
      keyboardType: keyboardType,
      controller: textEditingController,
      onEditingComplete: () {
        CommonMethods.closeKeyboard(context);
        onEditingComplete == null ? const SizedBox() : onEditingComplete();
      },
      onChanged: (value) {
        if (onChanged != null) {
          // Pass the value to the callback so it can be used by the caller
          onChanged(value);
        }
      },
      onSaved: (value) {
        onSaved();
      },
      scrollPadding: EdgeInsets.only(
          bottom:
              scrollBottomPadding ?? MediaQuery.of(context).size.height * 0.2),
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      decoration: appInputDecoration(hintText: hintText, border: border),
    );
  }
//endregion

  //region Comment
  static Widget commentTextFields({
    required BuildContext context,
    required TextEditingController textEditingController,
    required String hintText,
    int minLines = 1,
    int maxLines = 1,
    maxEntry = 20,
    bool enabled = true,
    dynamic onEditingComplete,
    dynamic onChanged,
    dynamic onSaved,
    TextInputType keyboardType = TextInputType.text,
    TextInputAction textInputAction = TextInputAction.newline,
    TextAlign textAlign = TextAlign.start,
    double border = 20,
    double? scrollBottomPadding,
    FocusNode? focusNode,
  }) {
    return TextFormField(
      enabled: enabled,
      maxLines: maxLines,
      focusNode: focusNode,
      minLines: minLines,
      textCapitalization: TextCapitalization.sentences,
      inputFormatters: [
        LengthLimitingTextInputFormatter(maxEntry),
      ],
      textAlign: textAlign,
      textInputAction: textInputAction,
      keyboardType: keyboardType,
      controller: textEditingController,
      onEditingComplete: () {
        CommonMethods.closeKeyboard(context);
        onEditingComplete == null ? const SizedBox() : onEditingComplete();
      },
      onChanged: (value) {
        onChanged == null ? Container() : onChanged();
      },
      onSaved: (value) {
        onSaved();
      },
      scrollPadding: EdgeInsets.only(
          bottom:
              scrollBottomPadding ?? MediaQuery.of(context).size.height * 0.2),
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      decoration: InputDecoration(
        isDense: true,

        // prefix: Text("Hello"),
        hintStyle: AppTextStyle.hintText(textColor: AppColors.writingBlack1),
        fillColor: AppColors.appWhite,
        filled: true,
        hintText: hintText,
        contentPadding: EdgeInsets.symmetric(vertical: 10, horizontal: 14.0),
        disabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        // border: OutlineInputBorder(
        //   borderSide: const BorderSide(
        //     color: AppColors.textFieldFill1,
        //     width: 1.0,
        //   ),
        //   borderRadius: BorderRadius.circular(border),
        // ),
        // focusedBorder: OutlineInputBorder(
        //   borderSide: const BorderSide(
        //     color: AppColors.textFieldFill1,
        //     width: 1.0,
        //   ),
        //   borderRadius: BorderRadius.circular(border),
        // ),
        // enabledBorder: OutlineInputBorder(
        //   borderSide: const BorderSide(
        //     color: AppColors.textFieldFill1,
        //     width: 1.0,
        //   ),
        //   borderRadius: BorderRadius.circular(border),
        // ),
        // // disabledBorder: OutlineInputBorder(
        // //   borderSide: const BorderSide(
        // //     color: AppColors.textFieldFill1,
        // //     width: 1.0,
        // //   ),
        // //   borderRadius: BorderRadius.circular(border),
        // // ),
        // focusedErrorBorder: OutlineInputBorder(
        //   borderSide: const BorderSide(
        //     color: AppColors.textFieldFill1,
        //     width: 1.0,
        //   ),
        //   borderRadius: BorderRadius.circular(border),
        // ),
        // errorBorder: OutlineInputBorder(
        //   borderSide: const BorderSide(
        //     color: AppColors.textFieldFill1,
        //     width: 1.0,
        //   ),
        //   borderRadius: BorderRadius.circular(border),
        // ),
      ),
    );
  }
//endregion

  //region Only string with space text field
  static Widget onlyStringWithSpaceTextField(
      {required BuildContext context,
      required TextEditingController textEditingController,
      required String hintText,
      int minLines = 1,
      int maxLines = 1,
      maxEntry = 20,
      bool enabled = true,
      dynamic onEditingComplete,
      dynamic onChanged,
      dynamic onSaved,
      Color fillColor = AppColors.textFieldFill1}) {
    return TextFormField(
      enabled: enabled,
      maxLines: maxLines,
      minLines: minLines,
      textCapitalization: TextCapitalization.sentences,
      textAlign: TextAlign.start,
      keyboardType: TextInputType.text,
      controller: textEditingController,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z\s]')),
        LengthLimitingTextInputFormatter(maxEntry),
      ],
      onEditingComplete: () {
        CommonMethods.closeKeyboard(context);
        onEditingComplete == null ? const SizedBox() : onEditingComplete();
      },
      onChanged: (value) {
        onChanged == null ? Container() : onChanged();
      },
      onSaved: (value) {
        onSaved();
      },
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      decoration: appInputDecoration(hintText: hintText, fillColor: fillColor),
    );
  }
//endregion

  //region Only string with space text field
  // static Widget emailTextField({
  //   required BuildContext context,
  //   required TextEditingController textEditingController,
  //   required String hintText,
  //   int minLines = 1,
  //   int maxLines = 1,
  //   bool enabled = true,
  //   dynamic onEditingComplete,
  //   dynamic onChanged,
  //   dynamic onSaved,
  //    Function(bool) onCheck,
  // }) {
  //   //Validation check
  //   bool isValidate(String email) {
  //     const pattern = r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$';
  //     final regExp = RegExp(pattern);
  //     return regExp.hasMatch(email);
  //   }
  //   //endregion
  //
  //
  //   return TextFormField(
  //     enabled: enabled,
  //     maxLines: maxLines,
  //     minLines: minLines,
  //     textCapitalization: TextCapitalization.none,
  //     textAlign: TextAlign.start,
  //     keyboardType:TextInputType.emailAddress ,
  //     controller: textEditingController,
  //
  //     inputFormatters: [
  //       FilteringTextInputFormatter.deny(RegExp(r'\s'))
  //     ],
  //     onEditingComplete: () {
  //       CommonMethods.closeKeyboard(context);
  //       onEditingComplete == null ? const SizedBox() : onEditingComplete();
  //     },
  //     onChanged: (value) {
  //       onChanged == null ? Container() : onChanged();
  //       final isValid = isValidate(textEditingController.text);
  //
  //     },
  //     onSaved: (value) {
  //       onSaved();
  //     },
  //     style:AppTextStyle.contentText0(textColor: AppColors.appBlack) ,
  //     decoration: appInputDecoration(hintText: hintText),
  //   );
  // }
//endregion

  //region Only number text field
  static Widget onlyNumberTextField(
      {required BuildContext context,
      required TextEditingController textEditingController,
      required String hintText,
      int minLines = 1,
      int maxLines = 1,
      maxEntry = 8,
      bool enabled = true,
      dynamic onEditingComplete,
      dynamic onChanged,
      dynamic onSaved,
      TextAlign? textAlign}) {
    return TextFormField(
      enabled: enabled,
      maxLines: maxLines,
      minLines: minLines,
      textCapitalization: TextCapitalization.sentences,
      textAlign: textAlign ?? TextAlign.start,
      keyboardType: TextInputType.number,
      controller: textEditingController,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(maxEntry),
        FilteringTextInputFormatter.singleLineFormatter,
      ],
      onEditingComplete: () {
        CommonMethods.closeKeyboard(context);
        onEditingComplete == null ? const SizedBox() : onEditingComplete();
      },

      onChanged: (value) {
        if (onChanged != null) {
          onChanged(value);
        }
      },

      // onChanged: (value) {
      //   onChanged == null ? Container() : onChanged();
      // },
      onSaved: (value) {
        onSaved();
      },
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      decoration: appInputDecoration(hintText: hintText),
    );
  }
//endregion

  //region Mobile number text field
  static Widget mobileNumberTextField({
    required BuildContext context,
    required TextEditingController textEditingController,
    required String hintText,
    int minLines = 1,
    int maxLines = 1,
    maxEntry = 8,
    bool enabled = true,
    dynamic onEditingComplete,
    dynamic onChanged,
    dynamic onSaved,
    dynamic onTap,
  }) {
    return TextFormField(
      enabled: enabled,
      maxLines: maxLines,
      minLines: minLines,
      textCapitalization: TextCapitalization.sentences,
      textAlign: TextAlign.start,
      keyboardType: TextInputType.number,
      controller: textEditingController,
      onTap: () {
        onTap == null ? const SizedBox() : onTap();
      },
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10)
      ],
      onEditingComplete: () {
        CommonMethods.closeKeyboard(context);
        onEditingComplete == null ? const SizedBox() : onEditingComplete();
      },
      onChanged: (value) {
        onChanged == null ? Container() : onChanged();
      },
      onSaved: (value) {
        onSaved();
      },
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      decoration: InputDecoration(
          prefixIcon: kIsWeb
              ? Container(
                  margin: const EdgeInsets.only(left: 16, bottom: 0),
                  width: CommonMethods.textWidth(
                      context: context,
                      textStyle: AppTextStyle.contentText0(
                          textColor: AppColors.appBlack),
                      text: "+91"),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "+91",
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack),
                  ))
              : Container(
                  padding: const EdgeInsets.only(left: 16, bottom: 1),
                  width: 1,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "+91",
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack),
                  )),

          // prefix: Text("+91",style:AppTextStyle.contentText0(textColor: AppColors.appBlack) ,),
          isDense: true,
          hintStyle: AppTextStyle.hintText(textColor: AppColors.writingBlack1),
          fillColor: AppColors.textFieldFill1,
          // Specify the desired internal color
          filled: true,
          hintText: hintText,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 10, horizontal: 14.0),
          border: OutlineInputBorder(
              borderSide: const BorderSide(
                color: AppColors.textFieldFill1,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(8.0)),
          focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: AppColors.textFieldFill1,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(
                  8.0) // Specify the desired border radius
              ),
          enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: AppColors.textFieldFill1,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(
                  8.0) // Specify the desired border radius
              ),
          disabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: AppColors.textFieldFill1,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(
                  8.0) // Specify the desired border radius
              ),
          focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: AppColors.textFieldFill1,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(
                  8.0) // Specify the desired border radius
              ),
          errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                color: AppColors.textFieldFill1,
                width: 1.0,
              ),
              borderRadius: BorderRadius.circular(
                  8.0) // Specify the desired border radius
              )),
    );
  }
//endregion

  //region User name text field
  static Widget userNameTextField(
      {required BuildContext context,
      required TextEditingController textEditingController,
      required String hintText,
      int minLines = 1,
      int maxLines = 1,
      maxEntry = 35,
      bool enabled = true,
      dynamic onEditingComplete,
      dynamic onChanged,
      dynamic onSaved,
      double? scrollBottomPadding,
      String? prefix}) {
    return TextFormField(
      enabled: enabled,
      maxLines: maxLines,
      minLines: minLines,
      textCapitalization: TextCapitalization.none,
      textAlign: TextAlign.start,
      keyboardType: TextInputType.text,
      controller: textEditingController,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9_.]')),
        FilteringTextInputFormatter.deny(RegExp(r'[ A-Z]')),
        LengthLimitingTextInputFormatter(maxEntry),
      ],
      onEditingComplete: () {
        CommonMethods.closeKeyboard(context);
        onEditingComplete == null ? const SizedBox() : onEditingComplete();
      },
      onChanged: (value) {
        //print(prefix);
        onChanged("${value}");

        // onChanged == null ? Container() : onChanged();
      },
      onSaved: (value) {
        onSaved();
      },
      scrollPadding: EdgeInsets.only(
          bottom:
              scrollBottomPadding ?? MediaQuery.of(context).size.height * 0.2),
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      decoration: appInputDecoration(hintText: hintText, prefix: prefix),
    );
  }
//endregion

  //region Website text field
  static Widget websiteTextField(
      {required BuildContext context,
      required TextEditingController textEditingController,
      required String hintText,
      int minLines = 1,
      int maxLines = 1,
      bool enabled = true,
      dynamic onEditingComplete,
      dynamic onChanged,
      dynamic onSaved,
      double? scrollBottomPadding}) {
    return TextFormField(
      enabled: enabled,
      maxLines: maxLines,
      minLines: minLines,
      textCapitalization: TextCapitalization.none,
      textAlign: TextAlign.start,
      keyboardType: TextInputType.url,
      controller: textEditingController,
      inputFormatters: [
        // FilteringTextInputFormatter.allow(RegExp(r'^[a-zA-Z0-9:/._-]*$')),
        FilteringTextInputFormatter.deny(RegExp(r'\s'))
      ],
      onEditingComplete: () {
        CommonMethods.closeKeyboard(context);
        onEditingComplete == null ? const SizedBox() : onEditingComplete();
      },
      onChanged: (value) {
        onChanged == null ? Container() : onChanged();
      },
      onSaved: (value) {
        onSaved();
      },
      scrollPadding: EdgeInsets.only(
          bottom:
              scrollBottomPadding ?? MediaQuery.of(context).size.height * 0.2),
      style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
      decoration: appInputDecoration(hintText: hintText),
    );
  }
//endregion

//region App input decoration
  static InputDecoration appInputDecoration(
      {required String hintText,
      double border = 8.0,
      bool visibleValidInvalid = false,
      bool isValid = false,
      String invalidMessage = "invalid",
      String? validMessage,
      String? prefix,
      Color fillColor = AppColors.textFieldFill1}) {
    return InputDecoration(
      isDense: true,
      prefixIcon: prefix != null
          ? Container(
              height: CommonMethods.textHeight(
                  context: AppConstants.globalNavigator.currentContext!,
                  textStyle:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack)),
              width: CommonMethods.textWidth(
                  text: "test_",
                  context: AppConstants.globalNavigator.currentContext!,
                  textStyle:
                      AppTextStyle.contentText0(textColor: AppColors.appBlack)),
              alignment: Alignment.centerRight,
              // padding: const EdgeInsets.only(top: 10,left: 14,right: 0,bottom: 10),
              padding: const EdgeInsets.only(bottom: 1),
              child: Text(
                prefix,
                style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
              ),
            )
          : null,

      // prefix: Text("Hello"),
      hintStyle: AppTextStyle.hintText(textColor: AppColors.writingBlack1),
      fillColor: fillColor,
      filled: true,
      hintText: hintText,
      contentPadding: EdgeInsets.symmetric(
          vertical: 10, horizontal: prefix == null ? 14.0 : 0.0),
      border: OutlineInputBorder(
        borderSide: BorderSide(
          color: fillColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(border),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: fillColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(border),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: fillColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(border),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: fillColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(border),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: fillColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(border),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: fillColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(border),
      ),
      errorStyle: AppTextStyle.smallText(textColor: AppColors.red),
      errorText: visibleValidInvalid
          ? (isValid ? validMessage : invalidMessage)
          : null,
    );
  }
//endregion

//region App text field style
  static InputDecoration appTextFieldStyle({
    required String hintText,
  }) {
    return InputDecoration(
      isDense: true,
      // prefix: Text("Hello"),
      hintStyle: AppTextStyle.hintText(textColor: AppColors.writingBlack1),
      fillColor: AppColors.textFieldFill1,
      filled: true,
      hintText: hintText,
      contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
      border: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      errorStyle: AppTextStyle.smallText(textColor: AppColors.red),
    );
  }
//endregion

//region App text field style
  static InputDecoration appTextFieldStyle2({
    required String hintText,
     TextStyle hintTextStyle = const TextStyle(),
     Color fillColor = AppColors.textFieldFill1,
     Color focusColor = AppColors.brandGreen,


  }) {
    return InputDecoration(
      isDense: true,
      // prefix: Text("Hello"),
      hintStyle: hintTextStyle,
      fillColor: fillColor,
      filled: true,
      hintText: hintText,
      contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
      border: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: focusColor,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: const BorderSide(
          color: AppColors.textFieldFill1,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
      ),
      errorStyle: AppTextStyle.smallText(textColor: AppColors.red),
    );
  }
//endregion

static Widget productSlugTextField({
  required BuildContext context,
  required TextEditingController textEditingController,
  required String hintText,
  int minLines = 1,
  int maxLines = 1,
  int maxEntry = 50,
  bool enabled = true,
  dynamic onEditingComplete,
  dynamic onChanged,
  dynamic onSaved,
  double? scrollBottomPadding,
  String? prefix,
}) {
  return TextFormField(
    enabled: enabled,
    maxLines: maxLines,
    minLines: minLines,
    textCapitalization: TextCapitalization.none,
    textAlign: TextAlign.start,
    keyboardType: TextInputType.text,
    controller: textEditingController,
    inputFormatters: [
      FilteringTextInputFormatter.allow(RegExp(r'[a-z0-9\-]')), // only lowercase, digits, hyphen
      LengthLimitingTextInputFormatter(maxEntry),
    ],
    onEditingComplete: () {
      CommonMethods.closeKeyboard(context);
      if (onEditingComplete != null) onEditingComplete();
    },
    onChanged: (value) {
      if (onChanged != null) onChanged(value);
    },
    onSaved: (value) {
      if (onSaved != null) onSaved();
    },
    scrollPadding: EdgeInsets.only(
      bottom: scrollBottomPadding ?? MediaQuery.of(context).size.height * 0.2,
    ),
    style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
    decoration: appInputDecoration(
      hintText: hintText,
      prefix: prefix,
    ),
  );
}


}
