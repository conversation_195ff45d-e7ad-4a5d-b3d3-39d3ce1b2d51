import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/product_slug_code_response/product_slug_code_response.dart';
import 'package:swadesic/services/product_slug_code_services/product_slug_code_services.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/my_reach_text_controller/my_reach_text_controller.dart';

class AddEditProductFieldsBloc {
  // region Common Variables
  BuildContext context;
  bool isSellingPriceWarningVisible = false;
  bool isDiscountVisible = false;
  static bool _isAffiliatePromotionEnabled = false;
  bool isCommissionWarningVisible = false;
  static bool? isUrlValid;
  double discount = 0.0;
  double affiliateCommissionPercentage = 0.0;
  static String gender = "M";

  // Product slug/code availability variables
  late ProductSlugCodeServices productSlugCodeServices;
  bool? isProductSlugAvailable;
  bool? isProductCodeAvailable;
  Timer? _productSlugTimer;
  Timer? _productCodeTimer;

  // endregion

  //region Text Editing Controller
  static TextEditingController brandNameTextCtrl = TextEditingController();
  static TextEditingController productNameTextCtrl = TextEditingController();
  static TextEditingController productCategoryTextCtrl =
      TextEditingController();
  static TextEditingController promoLinkTextCtrl = TextEditingController();
  // static TextEditingController hashTagsTextCtrl = TextEditingController();
  static TextEditingController inStockTextCtrl = TextEditingController();
  static TextEditingController mrpTextCtrl = TextEditingController();
  static TextEditingController sellingPriceTextCtrl = TextEditingController();
  static TextEditingController affiliateCommissionTextCtrl =
      TextEditingController();
  static TextEditingController productSlugTextCtrl = TextEditingController();
  static TextEditingController productCodeTextCtrl = TextEditingController();
  // static TextEditingController productDescNameTextCtrl = TextEditingController();
  //endregion

  // Map<RegExp, TextStyle> pattern= {
  //   RegExp(AppConstants.acceptAll):const TextStyle(color:AppColors.brandGreen,fontFamily:AppConstants.rRegular, fontSize: 14,fontWeight: FontWeight.w400),
  // };

  static MyReachTextController hashTagsTextCtrl =
      MyReachTextController(patternMatchMap: {
    RegExp(AppConstants.hashTag):
        AppTextStyle.contentText0(textColor: AppColors.brandBlack),
  }, onMatch: (List<String> match) {});

  static MyReachTextController productDescNameTextCtrl =
      MyReachTextController(patternMatchMap: {
    RegExp(AppConstants.atTag):
        AppTextStyle.contentText0(textColor: AppColors.brandBlack),
  }, onMatch: (List<String> match) {});

  // static MyReachTextController productDescNameTextCtrl = MyReachTextController(
  //     patternMatchMap: {
  //       RegExp(AppConstants.acceptAll):
  //           const TextStyle(color: AppColors.brandGreen, fontFamily: AppConstants.rRegular, fontSize: 14, fontWeight: FontWeight.w400),
  //     },
  //     onMatch: (List<String> match) {
  //       Map<RegExp, TextStyle> pattern = {
  //         RegExp(AppConstants.acceptAll):
  //             const TextStyle(color: AppColors.brandGreen, fontFamily: AppConstants.rRegular, fontSize: 14, fontWeight: FontWeight.w400),
  //       };
  //     });

  //endregion

  //region Controller
  final imageCtrl = StreamController<bool>.broadcast();
  final productDiscountCtrl = StreamController<bool>.broadcast();
  final promotionLinkCtrl = StreamController<bool>.broadcast();
  final genderCtrl = StreamController<String>.broadcast();
  final affiliateCtrl = StreamController<bool>.broadcast();
  final productSlugAvailabilityCtrl = StreamController<bool>.broadcast();
  final productCodeAvailabilityCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  AddEditProductFieldsBloc(this.context);

  // endregion

  // region Init
  void init() {
    //
    // isUrlValid = CommonMethods.urlValidationCheck(url: promoLinkTextCtrl.text);
    productSlugCodeServices = ProductSlugCodeServices();
    onChangeSellingPrices();

    // Add listeners for product slug and code
    productSlugTextCtrl.addListener(() {
      onChangeProductSlug(value: productSlugTextCtrl.text);
    });

    productCodeTextCtrl.addListener(() {
      onChangeProductCode(value: productCodeTextCtrl.text);
    });
  }

// endregion

  //region On Select Gender
  void onSelectGender(String selectedGender) {
    gender = selectedGender;
    genderCtrl.sink.add(gender);
  }
//endregion

  //region On change selling prices
  onChangeSellingPrices() async {
    await Future.delayed(Duration.zero);

    //If any one of the field is empty
    if (AddEditProductFieldsBloc.mrpTextCtrl.text.isEmpty ||
        AddEditProductFieldsBloc.sellingPriceTextCtrl.text.isEmpty) {
      isSellingPriceWarningVisible = false;
      //Discount
      discount = 0.0;
      //Discount hide
      isDiscountVisible = false;

      //refresh
      productDiscountCtrl.sink.add(true);
      return;
    }
    //Check selling is greater then mrp
    if (double.parse(AddEditProductFieldsBloc.mrpTextCtrl.text) <
        double.parse(AddEditProductFieldsBloc.sellingPriceTextCtrl.text)) {
      isSellingPriceWarningVisible = true;
      //Discount hide
      isDiscountVisible = false;
      //Discount
      discount = 0.0;
    } else {
      isSellingPriceWarningVisible = false;
      double mrp = double.parse(AddEditProductFieldsBloc.mrpTextCtrl.text);
      double selling =
          double.parse(AddEditProductFieldsBloc.sellingPriceTextCtrl.text);
      //Discount
      discount = ((mrp - selling) / mrp) * 100;
      //Discount Visible
      isDiscountVisible = true;
    }
    //refresh
    productDiscountCtrl.sink.add(true);
  }
  //endregion

  //region Check url validation
  void checkUrlValidation() {
    //print(AddEditProductFieldsBloc.promoLinkTextCtrl.text);
    if (AddEditProductFieldsBloc.promoLinkTextCtrl.text.isEmpty) {
      isUrlValid = null;
    } else {
      isUrlValid = CommonMethods.urlValidationCheck(
          url: AddEditProductFieldsBloc.promoLinkTextCtrl.text);
    }
    //refresh
    promotionLinkCtrl.sink.add(true);
  }
//endregion

  // Getter for isAffiliatePromotionEnabled
  bool get isAffiliatePromotionEnabled => _isAffiliatePromotionEnabled;
  static bool get getIsAffiliatePromotionEnabled =>
      _isAffiliatePromotionEnabled;

  //region Toggle Affiliate
  void toggleAffiliate(bool value) {
    _isAffiliatePromotionEnabled = value;
    if (!value) {
      affiliateCommissionTextCtrl.clear();
      affiliateCommissionPercentage = 0.0;
      isCommissionWarningVisible = false;
    }
    affiliateCtrl.sink.add(_isAffiliatePromotionEnabled);
  }
  //endregion

  //region Calculate Affiliate Commission
  void calculateAffiliateCommission() {
    if (affiliateCommissionTextCtrl.text.isEmpty ||
        sellingPriceTextCtrl.text.isEmpty) {
      affiliateCommissionPercentage = 0.0;
      isCommissionWarningVisible = false;
      affiliateCtrl.sink.add(_isAffiliatePromotionEnabled);
      return;
    }

    double commission = double.parse(affiliateCommissionTextCtrl.text);
    double sellingPrice = double.parse(sellingPriceTextCtrl.text);

    // Check if commission is greater than selling price
    if (commission > sellingPrice) {
      isCommissionWarningVisible = true;
      affiliateCommissionPercentage = 0.0;
    } else {
      isCommissionWarningVisible = false;
      affiliateCommissionPercentage = (commission / sellingPrice) * 100;
    }
    affiliateCtrl.sink.add(_isAffiliatePromotionEnabled);
  }
  //endregion

  //region On change product slug
  void onChangeProductSlug({required String value}) {
    if (_productSlugTimer?.isActive ?? false) {
      _productSlugTimer!.cancel();
    }
    _productSlugTimer = Timer(const Duration(seconds: 1), () {
      checkProductSlugAvailability(value: value);
    });
  }
  //endregion

  //region On change product code
  void onChangeProductCode({required String value}) {
    if (_productCodeTimer?.isActive ?? false) {
      _productCodeTimer!.cancel();
    }
    _productCodeTimer = Timer(const Duration(seconds: 1), () {
      checkProductCodeAvailability(value: value);
    });
  }
  //endregion

  //region Check product slug availability
  checkProductSlugAvailability({required String value}) async {
    // If product slug field is empty then return
    if (value == "") {
      isProductSlugAvailable = null;
      productSlugAvailabilityCtrl.sink.add(true);
      return;
    }

    try {
      // API call to check product slug availability
      ProductSlugCodeAvailabilityResponse response = await productSlugCodeServices.checkProductSlugAvailability(
        storeReference: AppConstants.appData.storeReference!,
        searchQuery: value,
      );

      isProductSlugAvailable = response.available == "true" ? false : true; // true means available, false means not available
      productSlugAvailabilityCtrl.sink.add(true);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.unableToCheckProductSlug, context);
      isProductSlugAvailable = null;
      productSlugAvailabilityCtrl.sink.add(true);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.unableToCheckProductSlug, context);
      isProductSlugAvailable = null;
      productSlugAvailabilityCtrl.sink.add(true);
    }
  }
  //endregion

  //region Check product code availability
  checkProductCodeAvailability({required String value}) async {
    // If product code field is empty then return
    if (value == "") {
      isProductCodeAvailable = null;
      productCodeAvailabilityCtrl.sink.add(true);
      return;
    }

    try {
      // API call to check product code availability
      ProductSlugCodeAvailabilityResponse response = await productSlugCodeServices.checkProductCodeAvailability(
        storeReference: AppConstants.appData.storeReference!,
        searchQuery: value,
      );

      isProductCodeAvailable = response.available == "true" ? false : true; // true means available, false means not available
      productCodeAvailabilityCtrl.sink.add(true);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.unableToCheckProductCode, context);
      isProductCodeAvailable = null;
      productCodeAvailabilityCtrl.sink.add(true);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.unableToCheckProductCode, context);
      isProductCodeAvailable = null;
      productCodeAvailabilityCtrl.sink.add(true);
    }
  }
  //endregion

//region Dispose
  void dispose() {
    imageCtrl.close();
    productDiscountCtrl.close();
    promotionLinkCtrl.close();
    genderCtrl.close();
    affiliateCtrl.close();
    productSlugAvailabilityCtrl.close();
    productCodeAvailabilityCtrl.close();
    _productSlugTimer?.cancel();
    _productCodeTimer?.cancel();
  }
  //endregion
}
