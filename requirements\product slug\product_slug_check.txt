API: Check Product Slug or Product Code Availability
====================================================

Endpoint:
---------
GET /product_slug_code_availability/

Purpose:
--------
Check if a given `product_slug` or `product_code` is already used for a product 
within a specific store. This is used to ensure uniqueness before creating or updating a product.

Query Parameters:
-----------------
- store_reference (string, required): 
    The reference identifier of the store.
- search_type (string, required): 
    Type of field to check for availability. Allowed values:
    - "product_slug"
    - "product_code"
- search_query (string, required): 
    The value of the slug or code to check.

Responses:
----------
Success (200 OK):
{
    "message": "success",
    "available": "true" | "false"
}

- available = "true" → The slug or code is available (not in use).
- available = "false" → The slug or code is already in use.

Example Requests:
-----------------

1. Check if product slug is available:
   GET /product_slug_code_availability/?store_reference=STORE123&search_type=product_slug&search_query=sample-slug

2. Check if product code is available:
   GET /product_slug_code_availability/?store_reference=STORE123&search_type=product_code&search_query=SKU001

Curl Examples:
--------------

1. Check product slug:
   curl -G "http://yourdomain.com/product_slug_code_availability/" \
     --data-urlencode "store_reference=STORE123" \
     --data-urlencode "search_type=product_slug" \
     --data-urlencode "search_query=sample-product-slug"

2. Check product code:
   curl -G "http://yourdomain.com/product_slug_code_availability/" \
     --data-urlencode "store_reference=STORE123" \
     --data-urlencode "search_type=product_code" \
     --data-urlencode "search_query=SKU123456"

Notes:
------
- Ensure `deleted=False` products are considered in the logic.
- This endpoint can be used in product creation/edit forms for validation.
