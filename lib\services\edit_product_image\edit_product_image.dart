

//happy
import 'package:swadesic/model/getProduct_image_response/get_seller_product_detail.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/http_service.dart';
import 'package:swadesic/util/app_constants.dart';

class EditProductAndImageServices {
  // region Common Variables
  late HttpService httpService;

  // endregion

  // region | Constructor |
  EditProductAndImageServices() {
    httpService = HttpService();
  }

  // endregion
// region Edit Only Product
  Future<void> editOnlyProduct(
      {
        required String productName,
        required String brandName,
        required String productCategory,
        required String productDescription,
        required String promotionLink,
        required int inStock,
        required int mrpPrice,
        required int sellingPrice,
        required int storeid,
        required String productReference,
        required String swadeshiMade,
        required String swadeshiOwned,
        required String swadeshiBrand,
        required String hashTag,
        required String gender,
        required bool isAffiliatePromotionEnabled,
        required double affiliateCommissionAmount,
        required String productSlug,
        required String productCode,

      }
      ) async {
    // get body [for POST request]
    var body = {
      "product_name": productName,
      "brand_name": brandName,
      "product_category":productCategory,
      "product_description": productDescription,
      "promotion_link": promotionLink,
      "hashtags": hashTag,
      "in_stock": inStock,
      "mrp_price": mrpPrice,
      "selling_price":sellingPrice,
      "storeid": storeid,
      "product_reference":productReference,
      "swadeshi_owned": swadeshiOwned,
      "swadeshi_made": swadeshiMade,
      "swadeshi_brand": swadeshiBrand,
      "targeted_gender": gender,
      "is_affiliate_promotion_enabled": isAffiliatePromotionEnabled,
      "affiliate_commission_amount": affiliateCommissionAmount,
      "product_slug": productSlug,
      "product_code": productCode,
    };

    //print(body);

    var url = "${AppConstants.getEditProduct}$productReference/";

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.putApiCall(body,url);
    // // return response;
    // return Product.fromJson(response['data']);

    //print(response);
  }
// endregion

// region Update stock
  Future updateStock({required String productReference, required int inStock }) async {
    // get body [for POST request]
    var body = {
      "in_stock":inStock
    };

    //print(body);

    var url = "${AppConstants.updateStock}$productReference/";

    // endregion
    Map<String, dynamic> response;

    //#region Region - Execute Request
    response = await httpService.patchApi(body,url);
    // return response;

  }
// endregion


}
